-- 爬虫任务管理服务 MySQL 初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS crawler_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE crawler_db;

-- 创建Dapr状态存储表
-- Dapr会自动创建这个表，但我们可以预先创建以确保结构正确
CREATE TABLE IF NOT EXISTS state (
    id VARCHAR(255) NOT NULL PRIMARY KEY,
    value LONGTEXT,
    isbinary BOOLEAN NOT NULL DEFAULT FALSE,
    insertdate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedate TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    eTag VARCHAR(36)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_state_updatedate ON state(updatedate);
CREATE INDEX IF NOT EXISTS idx_state_insertdate ON state(insertdate);

-- 创建用户（可选，用于生产环境）
-- CREATE USER IF NOT EXISTS 'crawler_user'@'%' IDENTIFIED BY 'crawler_password';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON crawler_db.* TO 'crawler_user'@'%';
-- FLUSH PRIVILEGES;

-- 显示表结构
DESCRIBE state;
