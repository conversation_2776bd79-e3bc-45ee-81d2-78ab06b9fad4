# MySQL状态存储配置指南

## 概述

本指南介绍如何将Dapr状态存储从Redis切换到MySQL。应用代码无需修改，只需要更改Dapr组件配置。

## 配置文件

### MySQL状态存储配置
文件：`dapr/components/statestore.yaml`（当前配置）
```yaml
apiVersion: dapr.io/v1alpha1
kind: Component
metadata:
  name: statestore
spec:
  type: state.mysql
  version: v1
  metadata:
  - name: connectionString
    value: "root:password@tcp(localhost:3306)/crawler_db?allowNativePasswords=true"
  - name: tableName
    value: "state"
  - name: schemaName
    value: "crawler_db"
  - name: pemPath
    value: ""
  - name: actorStateStore
    value: "true"
scopes:
- crawler-task-manager
```

### 备用Redis配置
文件：`dapr/components/statestore-redis.yaml`
```yaml
apiVersion: dapr.io/v1alpha1
kind: Component
metadata:
  name: statestore
spec:
  type: state.redis
  version: v1
  metadata:
  - name: redisHost
    value: localhost:6379
  - name: redisPassword
    value: ""
  - name: actorStateStore
    value: "true"
scopes:
- crawler-task-manager
```

## 快速开始

### 1. 启动MySQL
```bash
# 使用Docker Compose启动MySQL
make mysql-start

# 或者手动启动
docker-compose -f docker-compose-mysql.yml up -d
```

### 2. 验证MySQL连接
```bash
# 查看MySQL日志
make mysql-logs

# 连接到MySQL数据库
make mysql-connect

# 在MySQL中验证数据库和表
SHOW DATABASES;
USE crawler_db;
SHOW TABLES;
DESCRIBE state;
```

### 3. 启动应用
```bash
# 确保使用MySQL配置（默认已配置）
make switch-to-mysql

# 启动应用
make dapr-run-dev
```

### 4. 测试API
```bash
# 测试创建任务
make test-api

# 手动测试
curl -X POST http://localhost:8080/api/v1/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "MySQL测试任务",
    "description": "测试MySQL状态存储",
    "initial_urls": [{"url": "https://example.com", "method": "GET"}],
    "priority": 2,
    "downloader_name": "default_downloader",
    "parser_name": "default_parser"
  }'
```

### 5. 验证数据存储
```bash
# 连接到MySQL
make mysql-connect

# 查看存储的数据
SELECT id, LEFT(value, 100) as value_preview FROM state;
SELECT COUNT(*) as total_records FROM state;
```

## 配置参数说明

### MySQL连接字符串格式
```
[username[:password]@][protocol[(address)]]/dbname[?param1=value1&...&paramN=valueN]
```

### 常用参数
- `allowNativePasswords=true`: 允许使用原生密码认证
- `charset=utf8mb4`: 设置字符集
- `parseTime=true`: 解析时间类型
- `loc=Local`: 设置时区

### 生产环境配置示例
```yaml
metadata:
- name: connectionString
  value: "crawler_user:secure_password@tcp(mysql-server:3306)/crawler_db?allowNativePasswords=true&charset=utf8mb4&parseTime=true"
- name: tableName
  value: "dapr_state"
- name: schemaName
  value: "crawler_production"
```

## 切换存储后端

### 切换到MySQL
```bash
make switch-to-mysql
```

### 切换到Redis
```bash
make switch-to-redis
```

### 重启服务
```bash
# 停止当前服务
make dapr-stop

# 重新启动
make dapr-run-dev
```

## 数据迁移

如果需要从Redis迁移到MySQL，可以：

1. 导出Redis数据
2. 转换格式
3. 导入到MySQL

注意：Dapr状态存储的数据格式在不同后端之间是兼容的。

## 性能优化

### 数据库索引
```sql
-- 为常用查询添加索引
CREATE INDEX idx_state_id ON state(id);
CREATE INDEX idx_state_updatedate ON state(updatedate);
```

### 连接池配置
在连接字符串中添加：
```
?maxOpenConns=25&maxIdleConns=5&connMaxLifetime=300s
```

## 故障排除

### 常见问题

1. **连接失败**
   ```bash
   # 检查MySQL是否运行
   docker ps | grep mysql
   
   # 检查端口
   netstat -an | grep 3306
   ```

2. **权限问题**
   ```sql
   -- 创建用户并授权
   CREATE USER 'crawler_user'@'%' IDENTIFIED BY 'password';
   GRANT ALL PRIVILEGES ON crawler_db.* TO 'crawler_user'@'%';
   FLUSH PRIVILEGES;
   ```

3. **字符集问题**
   ```sql
   -- 检查字符集
   SHOW VARIABLES LIKE 'character_set%';
   
   -- 修改数据库字符集
   ALTER DATABASE crawler_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

## 监控和维护

### 查看状态存储使用情况
```sql
-- 查看记录数量
SELECT COUNT(*) FROM state;

-- 查看数据大小
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'crawler_db' AND table_name = 'state';

-- 查看最近更新的记录
SELECT id, updatedate FROM state ORDER BY updatedate DESC LIMIT 10;
```

### 清理旧数据
```sql
-- 删除超过30天的记录（根据需要调整）
DELETE FROM state WHERE updatedate < DATE_SUB(NOW(), INTERVAL 30 DAY);
```
