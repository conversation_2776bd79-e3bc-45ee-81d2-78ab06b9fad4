# 状态存储后端对比

## 概述

爬虫任务管理服务支持两种Dapr状态存储后端：Redis和MySQL。本文档对比两种方案的特点和适用场景。

## 功能对比

| 特性 | Redis | MySQL |
|------|-------|-------|
| **数据类型** | 键值存储 | 关系型数据库 |
| **持久化** | 可配置 | 默认持久化 |
| **查询能力** | 基础键值查询 | 复杂SQL查询 |
| **事务支持** | 有限支持 | 完整ACID事务 |
| **性能** | 极高（内存） | 高（磁盘+缓存） |
| **扩展性** | 水平扩展 | 垂直+水平扩展 |
| **运维复杂度** | 低 | 中等 |
| **数据一致性** | 最终一致性 | 强一致性 |

## 性能对比

### Redis
- **读写性能**: 极高，内存操作
- **延迟**: 亚毫秒级
- **吞吐量**: 10万+ ops/秒
- **内存使用**: 高，数据全部在内存

### MySQL
- **读写性能**: 高，磁盘+缓存
- **延迟**: 毫秒级
- **吞吐量**: 1万+ ops/秒
- **磁盘使用**: 数据持久化到磁盘

## 适用场景

### 选择Redis的场景
- 需要极高的读写性能
- 数据量相对较小（能放入内存）
- 对数据持久性要求不高
- 简单的键值查询需求
- 快速原型开发

### 选择MySQL的场景
- 需要数据持久化保证
- 大量数据存储
- 复杂查询需求
- 需要事务保证
- 生产环境长期运行
- 需要数据分析和报表

## 配置对比

### Redis配置
```yaml
spec:
  type: state.redis
  metadata:
  - name: redisHost
    value: localhost:6379
  - name: redisPassword
    value: ""
```

### MySQL配置
```yaml
spec:
  type: state.mysql
  metadata:
  - name: connectionString
    value: "root:password@tcp(localhost:3306)/crawler_db"
  - name: tableName
    value: "state"
  - name: schemaName
    value: "crawler_db"
```

## 数据结构

### Redis存储格式
```
Key: task:uuid
Value: {"id":"uuid","name":"任务名","status":"pending",...}

Key: task:list
Value: ["uuid1","uuid2","uuid3",...]
```

### MySQL存储格式
```sql
CREATE TABLE state (
    id VARCHAR(255) PRIMARY KEY,     -- task:uuid
    value LONGTEXT,                  -- JSON数据
    isbinary BOOLEAN DEFAULT FALSE,
    insertdate TIMESTAMP,
    updatedate TIMESTAMP,
    eTag VARCHAR(36)
);
```

## 切换指南

### 从Redis切换到MySQL
```bash
# 1. 启动MySQL
make mysql-start

# 2. 切换配置
make switch-to-mysql

# 3. 重启服务
make dapr-stop
make dapr-run-dev
```

### 从MySQL切换到Redis
```bash
# 1. 启动Redis
make redis-start

# 2. 切换配置
make switch-to-redis

# 3. 重启服务
make dapr-stop
make dapr-run-dev
```

## 监控和运维

### Redis监控
```bash
# 连接Redis
docker exec -it redis-crawler redis-cli

# 查看内存使用
INFO memory

# 查看键数量
DBSIZE

# 查看特定键
KEYS task:*
```

### MySQL监控
```bash
# 连接MySQL
make mysql-connect

# 查看表大小
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'crawler_db';

# 查看记录数
SELECT COUNT(*) FROM state;
```

## 性能优化建议

### Redis优化
- 配置合适的内存策略
- 启用持久化（RDB/AOF）
- 使用连接池
- 监控内存使用

### MySQL优化
- 添加适当索引
- 配置连接池
- 优化缓存设置
- 定期清理旧数据

## 总结

- **开发阶段**: 推荐使用Redis，快速启动，性能优异
- **生产环境**: 推荐使用MySQL，数据安全，功能完整
- **高并发场景**: Redis更适合
- **数据分析需求**: MySQL更适合

两种方案都通过Dapr抽象层提供一致的API，可以根据需求灵活切换。
