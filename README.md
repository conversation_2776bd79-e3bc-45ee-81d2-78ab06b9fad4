# 爬虫任务管理服务

基于Go语言和Dapr框架实现的爬虫任务管理服务。

## 功能特性

- 创建爬虫任务
- 任务状态管理
- 任务列表查询
- 支持GET和POST请求的初始URL配置
- 任务优先级管理
- 下载器和解析器配置
- 支持多种状态存储后端（Redis、MySQL）
- 基于Dapr的微服务架构

## 项目结构

```
golden_crawler/
├── main.go                    # 服务入口点
├── go.mod                     # Go模块定义
├── config/
│   └── config.go             # 应用配置
├── models/
│   └── task.go               # 数据模型定义
├── services/
│   └── task_service.go       # 业务逻辑服务
├── handlers/
│   └── task_handler.go       # HTTP处理器
└── dapr/
    ├── config.yaml           # Dapr配置
    └── components/
        ├── statestore.yaml   # 状态存储组件
        ├── pubsub.yaml       # 发布订阅组件
        └── secretstore.yaml  # 密钥存储组件
```

## API接口

### 创建任务
```
POST /api/v1/tasks
```

请求体示例：
```json
{
  "name": "示例爬虫任务",
  "description": "这是一个示例爬虫任务",
  "initial_urls": [
    {
      "url": "https://example.com",
      "method": "GET"
    },
    {
      "url": "https://api.example.com/data",
      "method": "POST",
      "body": "{\"key\": \"value\"}"
    }
  ],
  "priority": 2,
  "downloader_name": "default_downloader",
  "parser_name": "default_parser"
}
```

### 获取任务列表
```
GET /api/v1/tasks?limit=10&offset=0
```

### 获取单个任务
```
GET /api/v1/tasks/{id}
```

### 更新任务状态
```
PUT /api/v1/tasks/{id}/status
```

请求体：
```json
{
  "status": "running"
}
```

### 删除任务
```
DELETE /api/v1/tasks/{id}
```

## 运行服务

### 前置条件
- Go 1.21+
- Dapr CLI
- Redis 或 MySQL (用于状态存储)
- Docker 和 Docker Compose

### 快速开始

#### 使用Redis作为状态存储
```bash
# 1. 安装依赖
make deps

# 2. 启动Redis并设置开发环境
make dev-setup

# 3. 启动服务
make dapr-run-dev
```

#### 使用MySQL作为状态存储
```bash
# 1. 安装依赖
make deps

# 2. 启动MySQL并设置开发环境
make dev-setup-mysql

# 3. 切换到MySQL配置（如果需要）
make switch-to-mysql

# 4. 启动服务
make dapr-run-dev
```

### 手动启动

#### 使用Redis
```bash
# 启动Redis
make redis-start

# 启动服务
dapr run -f dapr/run-dev.yaml
```

#### 使用MySQL
```bash
# 启动MySQL
make mysql-start

# 启动服务
dapr run -f dapr/run-dev.yaml
```

### 测试API
```bash
# 使用Makefile快速测试
make test-api

# 或手动测试
curl -X POST http://localhost:8080/api/v1/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试任务",
    "description": "这是一个测试任务",
    "initial_urls": [
      {
        "url": "https://httpbin.org/get",
        "method": "GET"
      }
    ],
    "priority": 2,
    "downloader_name": "default_downloader",
    "parser_name": "default_parser"
  }'

# 获取任务列表
curl http://localhost:8080/api/v1/tasks
```

### 状态存储切换

项目支持Redis和MySQL两种状态存储后端：

```bash
# 切换到Redis
make switch-to-redis

# 切换到MySQL
make switch-to-mysql

# 重启服务以应用新配置
make dapr-stop
make dapr-run-dev
```

详细的MySQL配置说明请参考：[MySQL配置指南](docs/mysql-setup.md)

## 环境变量

- `SERVER_PORT`: 服务器端口 (默认: 8080)
- `SERVER_HOST`: 服务器主机 (默认: 0.0.0.0)
- `DAPR_APP_ID`: Dapr应用ID (默认: crawler-task-manager)
- `DAPR_APP_PORT`: Dapr应用端口 (默认: 8080)
- `DAPR_STATE_STORE`: 状态存储名称 (默认: statestore)
- `DAPR_PUBSUB_NAME`: 发布订阅名称 (默认: pubsub)
- `DAPR_SECRET_STORE`: 密钥存储名称 (默认: secretstore)

## 开发说明

### 已实现功能
- ✅ **数据持久化**: 已实现基于Dapr状态存储的任务CRUD操作
- ✅ **多存储后端**: 支持Redis和MySQL状态存储
- ✅ **错误处理**: 完善的错误处理和日志记录
- ✅ **配置管理**: 基于环境变量的配置系统

### 待实现功能
1. **事件发布**: 实现任务创建、状态变更等事件的发布到Dapr pub/sub
2. **任务队列**: 实现任务调度和队列管理
3. **认证授权**: 添加API认证和授权机制
4. **监控指标**: 添加服务监控和指标收集
5. **数据迁移**: 实现不同存储后端之间的数据迁移工具

### 可用命令
运行 `make help` 查看所有可用的命令。

### 项目文档
- [MySQL配置指南](docs/mysql-setup.md)
- [Dapr配置说明](dapr/README.md)
