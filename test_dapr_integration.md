# Dapr状态存储集成测试指南

## 功能概述

已成功实现了将爬虫任务保存到Dapr状态存储的功能，包括：

### 实现的功能

1. **CreateTask** - 创建任务并保存到Dapr状态存储
   - 生成唯一任务ID
   - 保存任务详细信息到状态存储
   - 维护任务ID列表用于分页查询

2. **GetTask** - 从Dapr状态存储获取任务
   - 根据任务ID获取任务详细信息
   - 处理任务不存在的情况

3. **UpdateTaskStatus** - 更新任务状态
   - 获取现有任务
   - 更新状态和更新时间
   - 保存到状态存储

4. **ListTasks** - 分页获取任务列表
   - 从状态存储获取任务ID列表
   - 支持分页（limit和offset）
   - 批量获取任务详细信息

5. **DeleteTask** - 删除任务
   - 从状态存储删除任务数据
   - 从任务列表中移除任务ID

6. **Close** - 优雅关闭Dapr客户端

### 数据存储结构

- **任务数据**: `task:{taskID}` -> 完整的任务JSON数据
- **任务列表**: `task:list` -> 任务ID数组，用于分页查询

### 配置要求

- Dapr状态存储组件名称：`statestore`（在配置中定义）
- Redis作为后端存储（已在dapr/components/statestore.yaml中配置）

## 测试步骤

### 1. 启动Redis
```bash
make redis-start
```

### 2. 启动服务
```bash
# 开发环境
dapr run -f dapr/run-dev.yaml

# 或者生产环境
dapr run -f dapr/run-prod.yaml
```

### 3. 测试API

#### 创建任务
```bash
curl -X POST http://localhost:8080/api/v1/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试爬虫任务",
    "description": "这是一个测试任务",
    "initial_urls": [
      {
        "url": "https://example.com",
        "method": "GET"
      }
    ],
    "priority": 2,
    "downloader_name": "default_downloader",
    "parser_name": "default_parser"
  }'
```

#### 获取任务
```bash
curl http://localhost:8080/api/v1/tasks/{task_id}
```

#### 获取任务列表
```bash
curl "http://localhost:8080/api/v1/tasks?limit=10&offset=0"
```

#### 更新任务状态
```bash
curl -X PUT http://localhost:8080/api/v1/tasks/{task_id}/status \
  -H "Content-Type: application/json" \
  -d '{"status": "running"}'
```

#### 删除任务
```bash
curl -X DELETE http://localhost:8080/api/v1/tasks/{task_id}
```

### 4. 验证Redis中的数据
```bash
# 连接到Redis
docker exec -it redis-crawler redis-cli

# 查看所有键
KEYS *

# 查看任务列表
GET task:list

# 查看特定任务
GET task:{task_id}
```

## 错误处理

所有方法都包含了完善的错误处理：
- Dapr客户端连接错误
- 状态存储操作错误
- JSON序列化/反序列化错误
- 任务不存在错误

## 后续改进

标记为TODO的功能：
1. 发布任务创建/状态变更/删除事件到Dapr pub/sub
2. 添加任务到任务队列
3. 清理相关资源

## 性能考虑

- ListTasks方法会为每个任务执行单独的GetState调用，对于大量任务可能需要优化
- 可以考虑使用Dapr的批量操作API来提高性能
- 任务列表使用简单数组存储，对于大量任务可能需要更高效的索引结构
